<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Config;
use App\Helpers\AppHelper;


class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Schema::defaultStringLength(191);

        // Read Emails
        Config::set('mail.mailbox_host', AppHelper::get('host', ''));
        Config::set('mail.mailbox_port', AppHelper::get('port', ''));
        Config::set('mail.mailbox_encryption', AppHelper::get('encryption', ''));
        Config::set('mail.mailbox_username', AppHelper::get('email', ''));
        Config::set('mail.mailbox_password', AppHelper::get('password', ''));


        // Send Emails
        Config::set('mail.mailers.smtp.host', AppHelper::get('sending_host', ''));
        Config::set('mail.mailers.smtp.port', AppHelper::get('sending_port', ''));
        Config::set('mail.mailers.smtp.encryption', AppHelper::get('sending_encryption', ''));
        Config::set('mail.mailers.smtp.username', AppHelper::get('sending_email', ''));
        Config::set('mail.mailers.smtp.password', AppHelper::get('sending_password', ''));
        Config::set('mail.from.address', AppHelper::get('sending_from_address', ''));
        Config::set('mail.from.name', AppHelper::get('sending_from_name', ''));
    }
}
