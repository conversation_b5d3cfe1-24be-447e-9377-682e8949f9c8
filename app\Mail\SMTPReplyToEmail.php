<?php
namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Symfony\Component\Mime\Email as SymfonyEmail;

class SMTPReplyToEmail extends Mailable
{
    use Queueable, SerializesModels;

    public $message_id;
    public $subject;
    public $body;
    public $attachFile;
    public $references;

    public function __construct(
        string $message_id,
        string $subject,
        string $body,
        array $attachFile = [],
        ?string $references = null
    ) {
        $this->message_id = $message_id;
        $this->subject = $subject;
        $this->body = $body;
        $this->attachFile = $attachFile;
        $this->references = $references;
    }

    public function build()
    {
        $email = $this->view('emails.reply')
            ->subject($this->subject)
            ->from(config('from'))
            ->with(['body' => $this->body])
            ->withSymfonyMessage(function (SymfonyEmail $message) {
                $messageId = $this->ensureAngleBrackets($this->message_id);

                $references = $this->references ?? '';
                $refs = array_filter(array_map('trim', explode(' ', $references)));
                $uniqueRefs = [];

                foreach ($refs as $ref) {
                    $formattedRef = $this->ensureAngleBrackets($ref);
                    if (!in_array($formattedRef, $uniqueRefs)) {
                        $uniqueRefs[] = $formattedRef;
                    }
                }

                if (!in_array($messageId, $uniqueRefs)) {
                    $uniqueRefs[] = $messageId;
                }

                $formattedReferences = implode(' ', $uniqueRefs);
                $message->getHeaders()->addTextHeader('In-Reply-To', $messageId);
                $message->getHeaders()->addTextHeader('References', $formattedReferences);
                $message->getHeaders()->addTextHeader('Thread-Index', $this->generateThreadIndex($uniqueRefs));
                $message->getHeaders()->addTextHeader('Thread-Topic', $this->subject);

                $message->getHeaders()->addTextHeader('X-Entity-Ref-ID', uniqid());
                $message->getHeaders()->addTextHeader('Precedence', 'bulk');
                $message->getHeaders()->addTextHeader('X-Priority', '1 (Highest)');

                // \Log::debug('Final Email Headers:', [
                //     'Message-ID' => $messageId,
                //     'In-Reply-To' => $messageId,
                //     'References' => $formattedReferences,
                //     'Thread-Index' => $this->generateThreadIndex($uniqueRefs),
                //     'Thread-Topic' => $this->subject
                // ]);
            });

        foreach ($this->attachFile as $file) {
            if ($file instanceof \Illuminate\Http\UploadedFile) {
                $email->attach($file->getRealPath(), [
                    'as' => $file->getClientOriginalName(),
                    'mime' => $file->getClientMimeType(),
                ]);
            } elseif (is_array($file) && isset($file['path'])) {
                $email->attach($file['path'], [
                    'as' => $file['name'],
                    'mime' => $file['mime'],
                ]);
            }
        }

        return $email;
    }

    private function ensureAngleBrackets($id)
    {
        $id = trim($id);
        if (!str_starts_with($id, '<')) {
            $id = '<' . $id;
        }
        if (!str_ends_with($id, '>')) {
            $id = $id . '>';
        }
        return $id;
    }

    private function generateThreadIndex($references)
    {
        $hash = substr(md5(implode('', $references)), 0, 16);
        return base64_encode(hex2bin($hash));
    }
}
// namespace App\Mail;

// use Illuminate\Bus\Queueable;
// use Illuminate\Contracts\Queue\ShouldQueue;
// use Illuminate\Mail\Mailable;
// use Illuminate\Mail\Mailables\Content;
// use Illuminate\Mail\Mailables\Envelope;
// use Illuminate\Queue\SerializesModels;
// use Symfony\Component\Mime\Email as SymfonyEmail;


// class SMTPReplyToEmail extends Mailable
// {
//     use Queueable, SerializesModels;

//     public $message_id;
//     public $subject;
//     public $body;
//     public $attachFile;


//     public function __construct(string $message_id, string $subject, string $body, array $attachFile = [])
//     {
//         $this->message_id = $message_id;
//         $this->subject = $subject;
//         $this->body = $body;
//         $this->attachFile = $attachFile;
//     }

//     public function build()
//     {
//         $originalMessageId = "{$this->message_id}";

//         $email = $this->view('emails.reply')
//             ->subject($this->subject)
//             ->from(config('from'))
//             ->with(['body' => $this->body])
//             ->withSymfonyMessage(function (SymfonyEmail $message) use ($originalMessageId) {
//                 $message->getHeaders()->addTextHeader('In-Reply-To', $originalMessageId);
//                 $message->getHeaders()->addTextHeader('References', $originalMessageId);
//             });

//         try {
//             foreach ($this->attachFile as $file) {
//                 if (
//                     isset($file['path'], $file['name'], $file['mime']) &&
//                     file_exists($file['path'])
//                 ) {
//                     $email->attach($file['path'], [
//                         'as' => $file['name'],
//                         'mime' => $file['mime'],
//                     ]);
//                 } else {
//                     \Log::warning('Attachment skipped due to missing keys or file not found:', $file);
//                 }
//             }
//         } catch (\Throwable $e) {
//             \Log::error('Attachment loop failed: ' . $e->getMessage());
//         }


//         return $email;
//     }

// }