<?php

namespace App\Http\Controllers;

use App\Models\FormData;
use App\Models\Form;
use Illuminate\Http\Request;
use App\Models\FormFunction;
use App\Models\FormField;

use App\Imports\FormDataImport;
use App\Exports\FormDataExport;
use Maatwebsite\Excel\HeadingRowImport;
use Maatwebsite\Excel\Facades\Excel;
use \Maatwebsite\Excel\Validators\ValidationException;
use DB;
use Carbon\Carbon;

class FormDataController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $request->validate([
            'form_id' => 'required'
        ]);

        try {
            $record = FormData::where('form_id', $request->form_id)->get();
            $headings = DB::table('form_fields')->where('form_id', $request->form_id)->get();
            return response()->json(['record' => $record, 'headings' => $headings]);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function getData(Request $request)
    {
        $request->validate([
            'form_id' => 'required',
        ]);

        try {
            $number = $request->phone_number;
            if ($number && $number[0] == '0') {
                $array = explode('0', $number, 2);
                $number = $array[1];
            }

            $data = FormData::where('phone_no', $number)->where('form_id', $request->form_id)->first();
            $fields = Form::where('id', $request->form_id)->with(['form_fields', 'form_fields.form_field_type', 'form_fields.form_field_options', 'form_functions'])->first()->toArray();
            // return response()->json($queue->forms()->with(['form_fields', 'form_fields.form_field_type', 'form_fields.form_field_options'])->get());
            if ($data) {
                $function = FormFunction::with(['function_type', 'reference_field'])->where('form_id', $data->form_id)->first();
                //age calculating function with provided DOB.
                if ($function && $function->function_type->function_name == 'Age Calculation' && $function->reference_field->type == 'date' && array_key_exists($function->reference_field->name, $data->data)) {
                    $value = $data->data[$function->reference_field->name];
                    $age = Carbon::parse($value)->diff(Carbon::now())->format('%y years, %m months and %d days');
                    //$age = Carbon::parse($value)->age;
                    $data[$function->name] = $age;
                }
            }
            return response()->json(['fields' => $fields, 'values' => $data]);

        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }


    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        $request->validate([
            'form_id' => 'required',
            'phone_number' => 'required',
            'call_id' => 'required',
        ]);

        try {
            $number = $request->phone_number;
            if ($number && $number[0] == '0') {
                $array = explode('0', $number, 2);
                $number = $array[1];
            }

            $record = FormData::where('phone_no', $number)->where('form_id', $request->form_id)->first();
            $form_headings = FormField::with('form_field_type')->where('form_id', $request->form_id)->where('hideable', 0)->get();
            $data = [];

            if ($record) {
                foreach ($form_headings as $key => $value) {
                    $field = strtolower($value->name);
                    if ($value->appendable || $value->editable) {

                        $data[$field] = $request->$field ?? null;

                    } else {
                        $data[$field] = $record->data[$field] ?? null;
                    }
                }
                $data_json = json_encode($data);
                $record->data = $data_json;
                $record->call_id = $request->call_id;
                $record->save();
                return response()->json('Record has been updated', 200);
            } else {
                $form_data = $request->except(['call_id', 'form_id']);
                foreach ($form_headings as $key => $value) {
                    $field = strtolower($value->name);
                    if ($request->has($field)) {
                        if ($value->appendable) {
                            if ($request->has($field)) {
                            } else {
                                $form_data[$field] = [];
                            }
                        } elseif ($value->form_field_type->html == 'date') {

                            $form_data[$field] = Carbon::parse($form_data[$field])->format('Y-m-d');
                        } elseif ($field == 'phone_number') {
                            $number = $form_data[$field];
                            if ($number && $number[0] == '0') {
                                $array = explode('0', $number, 2);
                                $number = $array[1];
                            }
                            $form_data[$field] = $number;
                        }
                    }
                }
                $formData = FormData::create([
                    'call_id' => $request->call_id,
                    'form_id' => $request->form_id,
                    'phone_no' => $form_data['phone_number'],
                    'data' => json_encode($form_data),
                ]);

                return response()->json('Record has been created', 200);
            }
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function importData(Request $request)
    {
        $request->validate([
            'file' => 'required',
            'form_id' => 'required'
        ]);
        try {
            $form_id = $request->form_id;
            $form_headings = DB::table('form_fields')->select('name')->where('form_id', $form_id)->pluck('name')->toArray();
            $lower_case_array = array_map('strtolower', $form_headings);
            $file_headings = (new HeadingRowImport)->toArray(request()->file('file'));
            $missing_headings = (array_diff($lower_case_array, $file_headings[0][0]));
            if ($missing_headings) {
                return response()->json(['errors' => ['These headings are missing in excel file' . ' ' . implode(',', $missing_headings)]], 500);
            }
            Excel::import(new FormDataImport($form_id), request()->file('file'));
            return response()->json('File has been uploaded!');
        } catch (\Throwable $th) {
            if ($th->failures()) {
                $failures = $th->failures();
                return response()->json($failures, 500);
            }
            return response()->json($th->getMessage(), 500);
        }
    }
    public function export(Request $request)
    {

        $data = FormData::where('form_id', $request->form_id)->get()->toArray();
        return Excel::download(new FormDataExport($data, $request->form_id), 'FormDataExport.xlsx');
    }

    public function show(Request $request, FormData $formData)
    {
        $formFields = FormField::where('form_id', $formData->form_id)->with("form_field_options")->get();
        return response()->json(['form_fields' => $formFields, 'data' => $formData->data]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\FormData  $formData
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, FormData $formData)
    {
        $formData->data = $request->all();
        $formData->save();
        return $request->all();
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\FormData  $formData
     * @return \Illuminate\Http\Response
     */
    public function destroy(FormData $formData)
    {
        try {
            $formData->delete();
            return response()->json('Record has been deleted');
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
}