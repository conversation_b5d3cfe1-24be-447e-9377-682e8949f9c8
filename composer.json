{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.1", "ext-json": "*", "ext-zip": "*", "anlutro/l4-settings": "^1.4", "beyondcode/laravel-websockets": "^1.13.2", "chan-sccp/pami": "^2.0", "guzzlehttp/guzzle": "^7.9", "kitloong/laravel-migrations-generator": "^7.0", "laravel/fortify": "^1.25", "laravel/framework": "^10.10", "laravel/horizon": "^5.31", "laravel/jetstream": "^4.3", "laravel/sanctum": "^3.3", "laravel/tinker": "^2.8", "league/flysystem-sftp-v3": "*", "livewire/livewire": "^3.0", "maatwebsite/excel": "^3.1", "marcelog/pagi": "^2.0", "matriphe/larinfo": "4.1.0", "mtrajano/laravel-swagger": "^0.6", "predis/predis": "^2.3", "pusher/pusher-php-server": "^7.2", "react/react": "^1.0", "santigarcor/laratrust": "*", "spatie/laravel-html": "*", "spatie/laravel-tags": "4.9.0", "webklex/laravel-imap": "^6.1"}, "require-dev": {"fakerphp/faker": "^1.24", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "1.6.12", "nunomaduro/collision": "^7.11", "phpunit/phpunit": "10.5.40", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}