<?php

namespace App\Http\Controllers;

use App\Models\Cdr;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Exports\InboundDispositionReportExport;
use Maatwebsite\Excel\Facades\Excel;

class InboundDispositionController extends Controller
{
    public function getInboundDisposition(): \Illuminate\Http\JsonResponse
    {
        try {
            $date = Carbon::now();
//            $cdr = Cdr::query()->whereDate('start', '>=', $date->startOfMonth())->
//            where('end', '<=', $date->endOfMonth())->where('lastapp', 'queue')->
//            join('queue_log', 'uniqueid', '=', 'queue_log.callid')->where('Event', 'WORKCODE')->
//            join('work_codes', 'id', '=', 'queue_log.data1')->
//            select(['uniqueid', 'src', 'dstchannel', 'start as date_time', 'disposition as call_status', 'work_codes.name as workcode'])->
//            orderBy('start', 'desc')->get()->map(function ($value, $key) {
//                $value->agent = explode('-', explode('/', $value->dstchannel)[1])[0];
//                return $value;
//            });
            $cdr = Cdr::query()->whereDate('start', '>=', $date->startOfMonth())->
            where('end', '<=', $date->endOfMonth())->where('lastapp', 'queue')->
            join('queue_log', 'uniqueid', '=', 'queue_log.callid')->where('Event', 'WORKCODE')->
            join('work_codes', 'id', '=', 'queue_log.data1')->
            select(['uniqueid', 'src', 'dstchannel', 'start as date_time', 'disposition as call_status', 'work_codes.name as workcode','agent'])->
            orderBy('start', 'desc')->get();
            return response()->json($cdr);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getFilteredInboundDisposition(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $subQuery = DB::table('queue_log')->where('Event', '=', 'CONNECT')->select(
                'callid', 
                'data2 as src', 
                DB::raw("(CASE WHEN Event = 'CONNECT' THEN 'ANSWERED' ELSE 'NO ANSWER' END) as call_status")
            );

            $queueLog = DB::table('queue_log')
                ->where('Event', '=', 'WORKCODE')
                ->join('work_codes', 'queue_log.data1', '=', 'work_codes.id')
                ->leftJoinSub($subQuery, 'enterqueue', function($join) {
                    $join->on('queue_log.callid', '=', 'enterqueue.callid');
                })
                ->select([
                    'queue_log.callid as uniqueid',
                    'queue_log.Agent as dstchannel',
                    'queue_log.Agent as agent',
                    'queue_log.time as date_time',
                    'work_codes.name as workcode',
                    DB::raw("COALESCE(enterqueue.src, queue_log.data1) as src"),
                    DB::raw("COALESCE(enterqueue.call_status, 'NO ANSWER') as call_status")
                ]);

            // Apply date range filter
            if ($request->has('range') && is_array($request->range)) {
                $start = Carbon::parse($request->range[0])->timezone('Asia/Karachi');
                $end = Carbon::parse($request->range[1])->timezone("Asia/Karachi");
                $queueLog->whereBetween('queue_log.time', [$start->toDateTime(), $end->toDateTime()]);
            }

            // Filter by agent
            if ($request->has('agent')) {
                $queueLog->where('queue_log.Agent', 'like', "%{$request->agent}%");
            }

            // Filter by destination queue
            if ($request->has('queue')) {
                $queueLog->where('queue_log.queuename', 'like', "%{$request->queue}%");
            }

            $result = $queueLog->get();
            
            return response()->json($result);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function exportInboundDisposition(Request $request)
    {
        try {
            $subQuery = DB::table('queue_log')->where('Event', '=', 'CONNECT')->select(
                'callid', 
                'data2 as src', 
                DB::raw("(CASE WHEN Event = 'CONNECT' THEN 'ANSWERED' ELSE 'NO ANSWER' END) as call_status")
            );

            $queueLog = DB::table('queue_log')
                ->where('Event', '=', 'WORKCODE')
                ->join('work_codes', 'queue_log.data1', '=', 'work_codes.id')
                ->leftJoinSub($subQuery, 'enterqueue', function($join) {
                    $join->on('queue_log.callid', '=', 'enterqueue.callid');
                })
                ->select([
                    'queue_log.callid as uniqueid',
                    'queue_log.Agent as dstchannel',
                    'queue_log.Agent as agent',
                    'queue_log.time as date_time',
                    'work_codes.name as workcode',
                    DB::raw("COALESCE(enterqueue.src, queue_log.data1) as src"),
                    DB::raw("COALESCE(enterqueue.call_status, 'NO ANSWER') as call_status")
                ]);

            // Apply date range filter
            if ($request->has('range') && is_array($request->range)) {
                $start = Carbon::parse($request->range[0])->timezone('Asia/Karachi');
                $end = Carbon::parse($request->range[1])->timezone("Asia/Karachi");
                $queueLog->whereBetween('queue_log.time', [$start->toDateTime(), $end->toDateTime()]);
            }

            // Filter by agent
            if ($request->has('agent')) {
                $queueLog->where('queue_log.Agent', 'like', "%{$request->agent}%");
            }

            // Filter by destination queue
            if ($request->has('queue')) {
                $queueLog->where('queue_log.queuename', 'like', "%{$request->queue}%");
            }

            // $result = $queueLog->get()->toArray();
            $result = $queueLog->get()->map(function ($item) {
                return [
                    'uniqueid' => $item->uniqueid,
                    'src' => $item->src,
                    'dstchannel' => $item->dstchannel,
                    'date_time' => $item->date_time,
                    'call_status' => $item->call_status,
                    'workcode' => $item->workcode,
                    'agent' => $item->agent,
                ];
            })->toArray();
    
            return Excel::download(new InboundDispositionReportExport($result), 'InboundDispositionReport.xlsx');

        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

}
