<?php

namespace App\Http\Controllers;

use App\Models\Cdr;
use App\Models\Queue;
use Carbon\Carbon;
use DateTime;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class MnPReportsController extends Controller
{
    public function agentStatus(Request $request)//
    {
        try {
            $user = $request->user();
            // dd($user->name);
            $agents  = DB::table('queue_log')->whereIn('Event', ['ADDMEMBER', 'REMOVEMEMBER', 'PAUSE', 'UNPAUSE', 'CONNECT'])
                ->where('time', 'LIKE', "{$request->date}%")
                ->whereBetween(DB::raw('TIME_FORMAT(time,"%H:%i")'), [$request->timeFrom, $request->timeTo])
                ->select(
                    DB::raw("Agent,
                CASE WHEN (Event = 'ADDMEMBER') THEN TIME_FORMAT(time,'%H:%i') ELSE '-'END AS 'Time in',
                CASE WHEN (Event = 'REMOVEMEMBER') THEN TIME_FORMAT(time,'%H:%i') ELSE '-' END AS 'Time out',
                Event,data1,data as status
            ")
                )
                ->get();

            foreach ($agents as $agent) {
                // if("PJSIP/{$user->auth_username}" == $agent->Agent )
                // {
                //     $agent->Agent = $user->name;
                // }
                if ($agent->Event == 'ADDMEMBER') {

                    $agent->status = 'Logged In';
                } elseif ($agent->Event == 'REMOVEMEMBER') {

                    $agent->status = 'Logged Out';
                } elseif ($agent->Event == 'PAUSE') {

                    $agent->status = 'Break';
                } elseif ($agent->Event == 'CONNECT' and $agent->data1 > 2) {

                    $agent->status = 'Hold';
                } elseif ($agent->Event == 'CONNECT' and $agent->data1 <= 2) {

                    $agent->status = 'On Call';
                }
            }
            return response()->json($agents);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
    public function callQueueSummaryReport(Request $request)//
    {
        try {
            $threshold = Queue::first('servicelevel');
            $cdr = DB::table('queue_log')->where('time', 'LIKE', "{$request->month}%")
                ->groupBy(DB::raw('Date(time)'))
                ->select(
                    DB::raw("
        Date(time),
        SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END) as totalInbounCalls,
        SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END) as totalAnswerCalls,
        SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END) as totalAbandonCalls,
        IFNULL(ROUND(SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= $threshold->servicelevel) THEN 1 ELSE 0 END)/SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END)*100),0) as CustomerServiceFactor,
        IFNULL(ROUND(SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)/SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END) *100),0) as percentageOfAnsweredCalls
        
        ")
                )->get();


            return response()->json($cdr);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
    public function hourlyAbandonReport(Request $request)//
    {

        $sum = 0;
        $counter = 0;
        $CURRENTDATE = Carbon::now()->format('Y-m-d');
        if ($CURRENTDATE == $request->date) {
            $cdr = DB::table('queue_log')
                ->rightJoin('hours', DB::raw('SUBSTRING(FROM_UNIXTIME((UNIX_TIMESTAMP(`time`) DIV (30* 60) ) * (30*60)),12)'), '=', DB::raw("hour AND Date(time) = '$request->date' "))
                ->whereRaw('CURRENT_TIME()>hour')
                ->whereBetween(DB::raw('hour'), ['08:00:00', '23:30:00'])
                ->groupBy(DB::raw('hour'))
                ->orderBy('hour')
                ->select(
                    DB::raw("
                        Date(time) as Date,
                        hour AS 'to', 
                        AddTime(hour, '00:30:00') AS 'from' ,
                        SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END) as totalInbounCalls,
                        SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END) as totalAbandonCalls,
                        IFNULL(ROUND(SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END)
                            /SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END)*100),0) percentageOfAbandonCalls,
                        SUM(CASE WHEN (Event = 'ADDMEMBER') THEN 1 ELSE 0 END) 
                            -SUM(CASE WHEN (Event = 'REMOVEMEMBER') THEN 1 ELSE 0 END) AS agentLogin")
                )
                ->get();
        } else {
            $cdr = DB::table('queue_log')
                ->rightJoin('hours', DB::raw('SUBSTRING(FROM_UNIXTIME((UNIX_TIMESTAMP(`time`) DIV (30* 60) ) * (30*60)),12)'), '=', DB::raw("hour AND Date(time) = '$request->date' "))
                ->whereBetween(DB::raw('hour'), ['08:00:00', '23:30:00'])
                ->groupBy(DB::raw('hour'))
                ->orderBy('hour')
                ->select(
                    DB::raw("
                    Date(time) as Date,
                    hour AS 'to', 
                    AddTime(hour, '00:30:00') AS 'from' ,
                    SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END) as totalInbounCalls,
                    SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END) as totalAbandonCalls,
                    IFNULL(ROUND(SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END)
                        /SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END)*100),0) percentageOfAbandonCalls,
                    SUM(CASE WHEN (Event = 'ADDMEMBER') THEN 1 ELSE 0 END) 
                        -SUM(CASE WHEN (Event = 'REMOVEMEMBER') THEN 1 ELSE 0 END) AS agentLogin")

                )
                ->get();
        }
        $CURRENTTIME = Carbon::now()->format('H');

        foreach ($cdr as  &$cd) {

            $sum = (int)$cd->agentLogin + $sum;
            if ($sum < 0) {
                $cd->agentLogin = 0;
            }

            if ((int)$CURRENTTIME < (int)substr($cd->to, 0, 2) and $CURRENTDATE == $request->date  or $sum < 0) {

                $cd->agentLogin = 0;
            } else {
                $cd->agentLogin = $sum;
            }


            if ($cd->Date == NULL) {
                $cd->Date = $request->date;
            }
        }
        return response()->json($cdr);
    }
    public function serviceLevelReport(Request $request)//
    {
        $threshold = Queue::first('servicelevel');
        if ($request->type == 'agent') {
            $reports  = DB::table('queue_log')->whereBetween(DB::raw('DATE(time)'), [$request->from, $request->to])
                ->whereIn('Event', ['CONNECT', 'ABANDON', 'RINGNOANSWER'])
                ->where('Agent', '!=', 'None')
                ->groupBy('agent')
                ->select(
                    DB::raw("
            agent,
            (SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= $threshold->servicelevel) THEN 1 ELSE 0 END)
                +SUM(CASE WHEN (Event = 'ABANDON' AND data3 <= $threshold->servicelevel) THEN 1 ELSE 0 END))
                /(SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)
                +SUM(CASE WHEN (Event = 'RINGNOANSWER') THEN 1 ELSE 0 END))*100 AS SLA
            ")
                )
                ->get();
        } else {
            $reports  = DB::table('queue_log')->whereBetween(DB::raw('DATE(time)'), [$request->from, $request->to])
                ->whereIn('Event', ['CONNECT', 'ABANDON'])
                ->groupBy(DB::raw("DATE(time)"))
                ->select(
                    DB::raw("
            Date(time) as date,
            
            (SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= $threshold->servicelevel) THEN 1 ELSE 0 END)
                +SUM(CASE WHEN (Event = 'ABANDON' AND data3 <= $threshold->servicelevel) THEN 1 ELSE 0 END))
                /(SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)
                +SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END))*100 AS SLA
            ")
                )
                ->get();
        }
        foreach ($reports as $report) {
            if ($report->SLA == NUll)
                $report->SLA = '0.00%';
            else
                $report->SLA = round($report->SLA, 2) . "%";
        }

        return response()->json($reports);
    }
    public function queueWiseReport(Request $request)
    {
        $threshold = Queue::first('servicelevel');
        

       

        $queue_log = DB::table('queue_log')
            ->whereBetween(DB::raw('DATE(time)'), [$request->from, $request->to])
            ->groupBy('queuename')
            ->select(
                DB::raw("
                    queuename,
                    SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END) as totalInbounCalls,
                    SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END) as totalAnswerCalls,
                    SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END) as totalAbandonCalls,
                    IFNULL(ROUND(SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= $threshold->servicelevel) THEN 1 ELSE 0 END)
                    /SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END)*100),0) as CustomerServiceFactor,
                    IFNULL(ROUND(SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)
                    /SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END) *100),0) as percentageOfAnsweredCalls")
            )->get();
        
        return response()->json($queue_log);
    }
    public function getAgentKPIReport(Request $request)//
    {

        $threshold = Queue::first('servicelevel');
        // $user = DB::table('users')->where('name', $request->agent)->first('auth_username');

        // $auth_username = "PJSIP/{$user->auth_username}";

        // $username = DB::table('users')->where('auth_username', $user->auth_username)->first('name');
        // $agentName = $username->name;
        $records = DB::select("Select 
            Date(time) as date,
            Agent,
            SUBSTRING(SEC_TO_TIME(SUM(CASE WHEN (Event = 'REMOVEMEMBER') THEN time_to_sec(time) ELSE 0 END)-SUM(CASE WHEN (Event = 'ADDMEMBER') THEN time_to_sec(time) ELSE 0 END)),1,8) As 'totalLoginTime',
                 SUBSTRING(SEC_TO_TIME(SUM(CASE WHEN (Event = 'REMOVEMEMBER') THEN time_to_sec(time) ELSE 0 END)-SUM(CASE WHEN (Event = 'ADDMEMBER') THEN time_to_sec(time) ELSE 0 END)),1,8) As 'totalSiginTime',
            SUBSTRING(SEC_TO_TIME(SUM(CASE WHEN (Event='UNPAUSE') THEN time_to_sec(time) ELSE 0 END)-SUM(CASE WHEN (Event='PAUSE') THEN time_to_sec(time) ELSE 0 END)),1,8) as totalBreakTime,
            SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END) as totalCalls,
            SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= $threshold->servicelevel) THEN 1 ELSE 0 END) as totalAnswer20Sec,
            SUBSTRING(SEC_TO_TIME(SUM(CASE WHEN (Event = 'COMPLETECALLER' OR Event ='COMPLETEAGENT') THEN data2 ELSE 0 END)),1,8) as totalTalkTime,
            SUBSTRING(SEC_TO_TIME(IFNULL((SUM(CASE WHEN (Event = 'COMPLETECALLER' OR Event ='COMPLETEAGENT') THEN data1 ELSE 0 END) 
            + SUM(CASE WHEN (Event = 'COMPLETECALLER' OR Event ='COMPLETEAGENT') THEN data2 ELSE 0 END)) /SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END),0)),1,8) as AHT
            FROM queue_log
            WHERE  DATE(time) BETWEEN '$request->from' AND '$request->to' and
             group by agent");

        foreach ($records as &$agent) {
            # code...
            // dd($agent->Agent);
            // if ($agent->Agent == $auth_username) {
            //     $agent->Agent = $agentName;
            // }
            if ($agent->totalLoginTime < 0) {
                $agent->totalLoginTime = "00:00:00";
            } else {
                $loginTime = new DateTime($agent->totalLoginTime);
                $breakTime = new DateTime($agent->totalBreakTime);
                $totalSiginTime = $breakTime->diff($loginTime);
                $time = $totalSiginTime->format('%h:%i:%s');
            }
            if ($agent->totalBreakTime < 0) {
                # code...
                $agent->totalBreakTime = "00:00:00";
            }
            if ($agent->totalSiginTime < 0) {
                # code...
                $agent->totalSiginTime = "00:00:00";
            } else {
                $agent->totalSiginTime = $time;
            }
        }


        return response()->json($records);
    }

    public function getCLIAbandonCalls(Request $request)
    {
        $array=[];
       
    
        $callIDs = DB::table('queue_log')->where('data2', $request->contactNumber)->get('callid');
        foreach ($callIDs as $key => $callID) {
         $cli=    DB::table('queue_log')->where('callid', $callID->callid)->where('Event', 'ABANDON')
         ->whereBetween(DB::raw('DATE(time)'), [$request->from, $request->to])
        ->get();
        foreach ($cli as $arr) {
            $arr->callid = $request->contactNumber;
            $arr->timeStart = Carbon::create($arr->time)->format('g:i A');
            $arr->timeEnd = Carbon::create($arr->time)->subSecond($arr->data3)->format('g:i A');
        }
        $array[] =$cli;
        
        }
        
       


        return response()->json($array);
    }
}
