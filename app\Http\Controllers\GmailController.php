<?php

namespace App\Http\Controllers;

use App\Mail\ReplyToEmail;
use App\Models\EmailSetting;
use Illuminate\Http\Request;
use Log;
use Mail;
use Webklex\IMAP\Facades\Client;
use Carbon\Carbon;

class GmailController extends Controller
{
    public function getEmailSettings()
    {
        return response()->json(EmailSetting::query()->get());
    }


    public function fetchEmailsBKP(Request $request)
    {
        try {
            $perPage = (int) $request->get('per_page', 50);
            $page = (int) $request->get('page', 1);
            $order = strtolower($request->get('order', 'desc'));

            // =========== Fetch Data My Table =========== 
            $settings = EmailSetting::pluck('value', 'key');

            $requiredKeys = ['host', 'port', 'encryption', 'email', 'password'];
            foreach ($requiredKeys as $key) {
                if (!isset($settings[$key])) {
                    return response()->json(['error' => "Missing email setting: $key"], 500);
                }
            }

            // =========== Connect to Email Server =========== 
            $client = Client::make([
                'host' => $settings['host'],
                'port' => (int) $settings['port'],
                'encryption' => $settings['encryption'],
                'validate_cert' => true,
                'username' => $settings['email'],
                'password' => $settings['password'],
            ]);

            $client->connect();

            // =========== Access Inbox =========== 
            $folder = $client->getFolder('INBOX');

            // =========== Fetch Emails =========== 
            $emailsQuery = $folder->query()
                ->since(Carbon::now()->subDays(1000000))
                ->get();

            // =========== Format Emails =========== 
            $emailsData = collect($emailsQuery)->map(function ($email) {
                $date = $email->getDate();
                $dateFormatted = $date instanceof Carbon ? $date->toDateTimeString() : Carbon::parse($date)->toDateTimeString();

                $emailId = $email->getMessageId();
                if (empty($emailId) || !is_string($emailId)) {
                    $emailId = md5($dateFormatted . ($email->getFrom()[0]->mail ?? 'unknown') . ($email->getSubject() ?? 'No Subject'));
                }

                $subject = !empty($email->getSubject()) ? trim($email->getSubject()) : 'No Subject';

                // =========== Attachments Email =========== 
                $attachments = [];
                foreach ($email->getAttachments() as $attachment) {
                    $attachments[] = [
                        'name' => $attachment->name,
                        'size' => $attachment->size,
                        'mime' => $attachment->content_type,
                        'base64' => base64_encode($attachment->content),
                    ];
                }

                // =========== Email Body Data Get =========== 
                $body = $email->getHTMLBody();
                foreach ($email->getAttachments() as $attachment) {
                    if ($attachment->disposition === 'inline' && strpos($attachment->content_type, 'image/') !== false) {
                        $cid = $attachment->id;
                        $base64Image = 'data:' . $attachment->content_type . ';base64,' . base64_encode($attachment->content);
                        $body = str_replace('cid:' . $cid, $base64Image, $body);
                    }
                }
                $formattedBody = str_replace(['<br>', '<br/>', '<br />'], '', preg_replace("/<br\s*\/?>\n?-{2,}<br\s*\/?>/i", '', nl2br(preg_replace("/\r\n\r\n/", "\n", $email->getTextBody() ?: strip_tags($body)))));

                $inReplyTo = $email->getHeader('In-Reply-To') ?? null;
                $references = $email->getHeader('References') ?? null;

                // =========== Response Email =========== 
                $emailData = [
                    'id' => $emailId,
                    'subject' => $subject,
                    'from' => $email->getFrom()[0]->personal ?? $email->getFrom()[0]->mail ?? 'Unknown',
                    'date' => $dateFormatted,
                    'body' => $formattedBody,
                    'attachments' => $attachments,
                    'in_reply_to' => $inReplyTo,
                    'references' => $references,
                ];

                // Add is_reply only for internal processing (not in final output)
                $emailData['_is_reply'] = stripos($subject, 'Re:') === 0;

                return $emailData;
            });

            // Sort emails by date
            $emailsData = $emailsData->sortBy('date', SORT_REGULAR, $order === 'desc')->values();

            // Separate original emails and replies using internal _is_reply field
            $originalEmails = $emailsData->filter(function ($email) {
                return !$email['_is_reply'];
            })->values();

            $replies = $emailsData->filter(function ($email) {
                return $email['_is_reply'];
            })->values();

            // Group replies by their original subject (remove "Re:" prefix)
            $groupedReplies = $replies->groupBy(function ($email) {
                return preg_replace('/^Re:\s*/i', '', $email['subject']);
            });

            // Combine original emails with their replies and remove _is_reply field
            $threadedEmails = $originalEmails->map(function ($email) use ($groupedReplies) {
                $originalSubject = $email['subject'];
                if (isset($groupedReplies[$originalSubject])) {
                    // Get last reply by date (latest)
                    $lastReply = $groupedReplies[$originalSubject]
                        ->sortByDesc(function ($reply) {
                            return Carbon::parse($reply['date'])->timestamp;
                        })->first();

                    if ($lastReply) {
                        unset($lastReply['_is_reply']);
                        $email['replies'] = $lastReply;
                    } else {
                        $email['replies'] = null;
                    }
                } else {
                    $email['replies'] = null;
                }
                unset($email['_is_reply']);
                return $email;
            });


            // Paginate the results
            $paginatedEmails = $threadedEmails->slice(($page - 1) * $perPage, $perPage)->values();

            return response()->json([
                'emails' => $paginatedEmails,
                'page' => $page,
                'per_page' => $perPage,
                'has_more' => $threadedEmails->count() > $page * $perPage,
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function fetchEmails(Request $request)
    {
        try {
            $perPage = (int) $request->get('per_page', 50);
            $page = (int) $request->get('page', 1);
            $order = strtolower($request->get('order', 'desc'));

            // ====== Fetch settings ======
            $settings = EmailSetting::pluck('value', 'key');
            $requiredKeys = ['host', 'port', 'encryption', 'email', 'password'];

            foreach ($requiredKeys as $key) {
                if (!isset($settings[$key])) {
                    return response()->json(['error' => "Missing email setting: $key"], 500);
                }
            }

            // ====== Connect to mail server ======
            $client = Client::make([
                'host' => $settings['host'],
                'port' => (int) $settings['port'],
                'encryption' => $settings['encryption'],
                'validate_cert' => true,
                'username' => $settings['email'],
                'password' => $settings['password'],
            ]);

            $client->connect();
            $folder = $client->getFolder('INBOX');

            // ====== Fetch emails ======
            $emailsQuery = $folder->query()
                ->since(Carbon::now()->subDays(1000000))
                ->get();

            $emailsData = collect($emailsQuery)->map(function ($email) {
                $date = $email->getDate();
                $dateFormatted = $date instanceof Carbon
                    ? $date->toDateTimeString()
                    : Carbon::parse($date)->toDateTimeString();

                $emailId = $email->getMessageId();
                if (empty($emailId) || !is_string($emailId)) {
                    $emailId = md5($dateFormatted . ($email->getFrom()[0]->mail ?? 'unknown') . ($email->getSubject() ?? 'No Subject'));
                }

                $subject = !empty($email->getSubject())
                    ? trim($email->getSubject())
                    : 'No Subject';

                // ====== Attachments ======
                $attachments = [];
                foreach ($email->getAttachments() as $attachment) {
                    $attachments[] = [
                        'name' => $attachment->name,
                        'size' => $attachment->size,
                        'mime' => $attachment->content_type,
                        'base64' => base64_encode($attachment->content),
                    ];
                }

                // ====== Inline images ======
                $body = $email->getHTMLBody();
                foreach ($email->getAttachments() as $attachment) {
                    if ($attachment->disposition === 'inline' && strpos($attachment->content_type, 'image/') !== false) {
                        $cid = $attachment->id;
                        $base64Image = 'data:' . $attachment->content_type . ';base64,' . base64_encode($attachment->content);
                        $body = str_replace('cid:' . $cid, $base64Image, $body);
                    }
                }

                $formattedBody = preg_replace([
                    "/<br\s*\/?>\n?-{2,}<br\s*\/?>/i",
                    "/\r\n\r\n+/",
                    "/^\s+|\s+$/",
                ], [
                    '',
                    "\n\n",
                    ''
                ], nl2br($email->getTextBody() ?: strip_tags($body)));

                return [
                    'id' => $emailId,
                    'subject' => $subject,
                    'from' => $email->getFrom()[0]->personal ?? $email->getFrom()[0]->mail ?? 'Unknown',
                    'date' => $dateFormatted,
                    'body' => $formattedBody,
                    'attachments' => $attachments,
                    '_is_reply' => stripos($subject, 'Re:') === 0,
                    '_original_subject' => preg_replace('/^Re:\s*/i', '', $subject)
                ];
            });

            // ====== Sorting ======
            $emailsData = $emailsData->sortBy('date', SORT_REGULAR, $order === 'desc')->values();

            // ====== Threading: group replies under their original message ======
            $originalEmails = $emailsData->filter(fn($email) => !$email['_is_reply'])->values();
            $replies = $emailsData->filter(fn($email) => $email['_is_reply'])->values();
            $groupedReplies = $replies->groupBy('_original_subject');

            $threadedEmails = $originalEmails->map(function ($email) use ($groupedReplies) {
                $originalSubject = $email['subject'];
                $email['replies'] = $groupedReplies->get($originalSubject, collect())->values()->all();
                return $email;
            });

            // ====== Pagination ======
            $paged = $threadedEmails->forPage($page, $perPage)->values();

            return response()->json([
                'emails' => $paged,
                'page' => $page,
                'per_page' => $perPage,
                'total' => $threadedEmails->count()
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function replyToMailBKP(Request $request)
    {
        $emailId = $request->get('email_id');
        $replyBody = $request->get('body');

        if (!$emailId || !$replyBody) {
            return response()->json(['error' => 'Missing email_id or body'], 400);
        }

        // ==== Fetch email settings ==== 
        $settings = EmailSetting::pluck('value', 'key');
        $requiredKeys = ['sending_host', 'sending_port', 'sending_encryption', 'sending_email', 'sending_password'];
        foreach ($requiredKeys as $key) {
            if (!isset($settings[$key])) {
                return response()->json(['error' => "Missing email setting: $key"], 500);
            }
        }

        Log::info("Email setting" ,  );

        // ===== Connect to mail smtp server === 
        $client = Client::make([
            'host' => $settings['sending_host'],
            'port' => (int) $settings['sending_port'],
            'encryption' => $settings['sending_encryption'],
            'validate_cert' => true,
            'username' => $settings['sending_email'],
            'password' => $settings['sending_password'],
        ]);
        $client->connect();
        // ===== Find target email =====
        $folder = $client->getFolder('INBOX');
        $emails = $folder->query()->since(Carbon::now()->subDays(100000))->get();

        $targetEmail = null;
        foreach ($emails as $email) {
            $date = $email->getDate();
            $dateFormatted = $date instanceof Carbon ? $date->toDateTimeString() : Carbon::parse($date)->toDateTimeString();

            $computedId = $email->getMessageId();
            if (empty($computedId) || !is_string($computedId)) {
                $computedId = md5($dateFormatted . ($email->getFrom()[0]->mail ?? 'unknown') . ($email->getSubject() ?? 'No Subject'));
            }

            if ($computedId === $emailId) {
                $targetEmail = $email;
                break;
            }
        }

        if (!$targetEmail) {
            return response()->json(['error' => 'Email not found.'], 404);
        }

        $replyTo = $targetEmail->getFrom()[0]->mail ?? null;
        if (!$replyTo) {
            return response()->json(['error' => 'Sender email not found.'], 400);
        }

        $originalMessageId = $targetEmail->getMessageId();
        $originalSubject = $targetEmail->getSubject();

        // ========== Handle attachments =================
        $attachments = [];
        if ($request->hasFile('attachments')) {
            foreach ($request->file('attachments') as $file) {
                $attachments[] = [
                    'file' => $file->getRealPath(),
                    'options' => [
                        'as' => $file->getClientOriginalName(),
                        'mime' => $file->getMimeType()
                    ]
                ];
            }
        }

        Mail::to($replyTo)->send(new ReplyToEmail($replyBody, $originalMessageId, $originalSubject, $attachments));

        return response()->json(['message' => "Reply sent to $replyTo"]);
    }



public function replyToMail(Request $request)
{
    $emailId = $request->get('email_id');
    $replyBody = $request->get('body');

    if (!$emailId || !$replyBody) {
        Log::warning("Missing email_id or body in request.", [
            'email_id' => $emailId,
            'body' => $replyBody
        ]);
        return response()->json(['error' => 'Missing email_id or body'], 400);
    }

    // ==== Fetch email settings ==== 
    $settings = EmailSetting::pluck('value', 'key');
    $requiredKeys = ['sending_host', 'sending_port', 'sending_encryption', 'sending_email', 'sending_password'];

    foreach ($requiredKeys as $key) {
        if (!isset($settings[$key])) {
            Log::error("Missing email setting: $key");
            return response()->json(['error' => "Missing email setting: $key"], 500);
        }
    }

    Log::info("Fetched email settings successfully.", $settings->toArray());

    // ===== Connect to mail smtp server === 
    try {
        $client = Client::make([
            'host' => $settings['sending_host'],
            'port' => (int) $settings['sending_port'],
            'encryption' => $settings['sending_encryption'],
            'validate_cert' => true,
            'username' => $settings['sending_email'],
            'password' => $settings['sending_password'],
        ]);
        $client->connect();
        Log::info("Connected to IMAP server successfully.");
    } catch (\Exception $e) {
        Log::error("Failed to connect to IMAP server.", ['error' => $e->getMessage()]);
        return response()->json(['error' => 'Could not connect to mail server.'], 500);
    }

    // ===== Find target email =====
    try {
        $folder = $client->getFolder('INBOX');
        $emails = $folder->query()->since(Carbon::now()->subDays(100000))->get();
        Log::info("Fetched " . count($emails) . " emails from INBOX.");
    } catch (\Exception $e) {
        Log::error("Failed to fetch emails.", ['error' => $e->getMessage()]);
        return response()->json(['error' => 'Failed to fetch emails.'], 500);
    }

    $targetEmail = null;
    foreach ($emails as $email) {
        $date = $email->getDate();
        $dateFormatted = $date instanceof Carbon ? $date->toDateTimeString() : Carbon::parse($date)->toDateTimeString();

        $computedId = $email->getMessageId();
        if (empty($computedId) || !is_string($computedId)) {
            $computedId = md5($dateFormatted . ($email->getFrom()[0]->mail ?? 'unknown') . ($email->getSubject() ?? 'No Subject'));
        }

        if ($computedId === $emailId) {
            $targetEmail = $email;
            Log::info("Target email found.", ['email_id' => $emailId]);
            break;
        }
    }

    if (!$targetEmail) {
        Log::warning("Email with ID $emailId not found.");
        return response()->json(['error' => 'Email not found.'], 404);
    }

    $replyTo = $targetEmail->getFrom()[0]->mail ?? null;
    if (!$replyTo) {
        Log::error("Sender email not found in the target email.");
        return response()->json(['error' => 'Sender email not found.'], 400);
    }

    $originalMessageId = $targetEmail->getMessageId();
    $originalSubject = $targetEmail->getSubject();

    // ========== Handle attachments =================
    $attachments = [];
    if ($request->hasFile('attachments')) {
        foreach ($request->file('attachments') as $file) {
            $attachments[] = [
                'file' => $file->getRealPath(),
                'options' => [
                    'as' => $file->getClientOriginalName(),
                    'mime' => $file->getMimeType()
                ]
            ];
        }
        Log::info("Attachments processed.", ['count' => count($attachments)]);
    } else {
        Log::info("No attachments found.");
    }

    try {
        Mail::to($replyTo)->send(new ReplyToEmail($replyBody, $originalMessageId, $originalSubject, $attachments));
        Log::info("Reply sent successfully.", ['to' => $replyTo]);
    } catch (\Exception $e) {
        Log::error("Failed to send reply email.", ['error' => $e->getMessage()]);
        return response()->json(['error' => 'Failed to send email.'], 500);
    }

    return response()->json(['message' => "Reply sent to $replyTo"]);
}


    public function update(Request $request, EmailSetting $emailSetting): \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'host' => ['required'],
            'port' => ['required', 'numeric'],
            'encryption' => ['required'],
            'email' => ['required'],
            'password' => ['required'],
        ]);
        try {
            foreach ($request->all() as $value => $index)
                EmailSetting::query()->where("key", "=", $value)->update([
                    "value" => $index
                ]);
            return response()->json("Email Settings has been updated.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage());
        }
    }

    public function replayUpdate(Request $request, EmailSetting $emailSetting): \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'sending_host' => ['required'],
            'sending_port' => ['required', 'numeric'],
            'sending_encryption' => ['required'],
            'sending_email' => ['required'],
            'sending_password' => ['required'],
        ]);
        try {
            foreach ($request->all() as $value => $index)
                EmailSetting::query()->where("key", "=", $value)->update([
                    "value" => $index
                ]);
            return response()->json("Email Reply Settings has been updated.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage());
        }
    }

}
