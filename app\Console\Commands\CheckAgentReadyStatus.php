<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;
use App\Models\SystemSetting;
use App\Models\User;
use App\Models\PauseReason;
use App\Models\WorkCode;
use App\Models\CampaignSetting;
use App\Models\Script;
use App\Models\QueueLog;
use App\Models\CallbackRequest;
use App\Events\IsReady;
use App\Events\AgentLogin;
use App\Events\GetPauseReason;
use App\Events\GetWorkCode;
use App\Events\AgentStatus;
use App\Events\GetQueues;
use App\Events\GetScriptByQueue;
use App\Events\GetCampagin;
use App\Events\GetUserCampagin;
use App\Events\GetUser;
use App\Events\GetSystemSetting;
use App\Events\GetAbandonCallReport;
use App\Events\GetCallbackRequest;
use PAMI\Client\Impl\ClientImpl;
use PAMI\Message\Action\QueueStatusAction;
use PAMI\Message\Event\QueueMemberEvent;
use Laravel\Sanctum\PersonalAccessToken;

class CheckAgentReadyStatus extends Command
{
    protected $signature = 'app:check-agent-ready-status';
    protected $description = 'Fetch Real Time Data For Socket';
    protected $users;
    protected $client;

    public function __construct()
    {
        parent::__construct();
        $this->users = $this->loggedInUsers();
        $this->client = new ClientImpl($this->getOptions());
    }

    public function handle()
    {
        $this->client->open();

        try {
            $this->is_ready();
            $this->is_login();
            $this->getPauseReason();
            $this->getWorkCode();
            $this->get_agent_status();
            $this->get_queue();
            $this->getScriptByQueue();
            $this->abandonCallReport();
            // $this->campaignSetting();
            $this->get_campaign();
            $this->getSystemSetting();
            $this->get_user();
            $this->callbackRequestReport();
        } finally {
            $this->client->close();
            unset($this->client);
        }

        sleep(1);
    }

    protected function loggedInUsersOLD() {
        $users = Cache::get('logged_in_users', []);
        $filteredUsers = User::whereIn('id', array_keys($users))
        ->with('queues')
        ->get()
        ->filter(fn($user) => $user->queues->isNotEmpty());

        return $filteredUsers;
    }

    protected function loggedInUsers()
    {
        $users = Cache::get('logged_in_users', []);
        $allUsers = \App\Models\User::whereIn('id', array_keys($users))
                ->with('queues')
                ->get();

        // Check if any user has a queue
        $hasAnyQueue = $allUsers->pluck('queues')->flatten()->isNotEmpty();

        // Apply queue filter only if at least one user has queues
        $filteredUsers = $hasAnyQueue
                ? $allUsers->filter(fn($user) => $user->queues->isNotEmpty())->values()
                : $allUsers;

        return $allUsers;
    }

    protected function getOptions(): array
    {
        return Cache::remember('ami_settings', 60, function () {
            return [
                'host' => SystemSetting::GetSetting('server_address'),
                'scheme' => 'tcp://',
                'port' => SystemSetting::GetSetting('manager_port'),
                'username' => SystemSetting::GetSetting('username'),
                'secret' => SystemSetting::GetSetting('secret'),
                'connect_timeout' => SystemSetting::GetSetting('connection_timeout'),
                'read_timeout' => SystemSetting::GetSetting('read_timeout')
            ];
        });
    }

    public function is_ready() {
        if($this->users) {
            foreach ($this->users as $user) {

                if (!$user->queues || $user->queues->isEmpty()) {
                    continue;
                }

                $queues = $user->queues;
                $response = null;
                foreach ($queues as $queue) {
                    $action = new QueueStatusAction($queue->name, "PJSIP/{$user->auth_username}");
                    $response = $this->client->send($action);
                }

                if ($response) {
                    $status = $response->getEvents()[1]->getKey('paused');
                    $statusData = ['user_id' => $user->id, 'status' => $status == 1 ? false : true];

                    broadcast(new IsReady($statusData))->toOthers();
                }

                unset($response);
            }
        }
    }

    public function is_login()
    {
        if($this->users) {
            foreach ($this->users as $user) {

                if (!$user->queues || $user->queues->isEmpty()) {
                    continue;
                }

                $queues = $user->queues;
                $response = null;
                foreach ($queues as $queue) {
                    $action = new QueueStatusAction($queue->name, "PJSIP/{$user->auth_username}");
                    $response = $this->client->send($action);
                }

                if ($response) {
                    $status = $response->getEvents()[1]->getKey('event');

                    $statusData = ['user_id' => $user->id, 'status' => $status === 'QueueMember' ? true : false];

                    broadcast(new AgentLogin($statusData))->toOthers();
                }

                unset($response);
            }
        }
    }

    public function getPauseReason()
    {
        if($this->users) {
            $pauseReason = Cache::remember('pause_reasons', 60, function () {
                return PauseReason::query()->get();
            });

            foreach ($this->users as $user) {
                // $pauseReason = PauseReason::query()->get();
                $pauseReasonData = ['user_id' => $user->id, 'data' => $pauseReason];
                broadcast(new GetPauseReason($pauseReasonData));
            }
        }
    }

    public function getWorkCode()
    {
        if($this->users) {
            $workCode = Cache::remember('work_codes', 60, function () {
                return WorkCode::query()->get();
            });

            foreach ($this->users as $user) {
                // $workCode = WorkCode::query()->get();
                $workCodeData = ['user_id' => $user->id, 'data' => $workCode];
                broadcast(new GetWorkCode($workCodeData));
            }
        }
    }

    public function get_agent_status()
    {
        if($this->users) {
            foreach ($this->users as $user) {

                if (!$user->queues || $user->queues->isEmpty()) {
                    continue;
                }

                $queues = $user->queues;
                $response = "Failed to fetch data.";
                foreach ($queues as $queue) {
                    $action = new QueueStatusAction($queue->name, "PJSIP/{$user->auth_username}");
                    $response = $this->client->send($action);
                }

                if ($response) {
                    $status = $response->getEvents()[1] instanceof QueueMemberEvent;
                    $statusData = ['user_id' => $user->id, 'status' => $status];
                }
                else {
                    $statusData = ['user_id' => $user->id, 'status' => "Failed to fetch agent status"];
                }

                broadcast(new AgentStatus($statusData))->toOthers();
                unset($response);
            }
        }
    }

    public function get_queue()
    {
        if($this->users) {
            foreach ($this->users as $user) {

                if (!$user->queues || $user->queues->isEmpty()) {
                    continue;
                }

                $queues = $user->queues->pluck('name');
                $queueData = ['user_id' => $user->id, 'data' => $queues];
                broadcast(new GetQueues($queueData));
            }
        }
    }

    public function getScriptByQueue()
    {
        if($this->users) {
            $script = Script::query()->where('status', true)->first();
            foreach ($this->users as $user) {
                if($script) {
                    $scriptData = ['user_id' => $user->id, 'queue' => $script->queue_name, 'data' => $script ? [$script] : []];
                    broadcast(new GetScriptByQueue($scriptData));
                }
            }
        }
    }

    public function campaignSetting(){
        if($this->users) {
            $data = CampaignSetting::first();
            foreach ($this->users as $user) {
                $campaignData = ['user_id' => $user->id, 'data' => $data];
                broadcast(new GetCampagin($campaignData));
            }
        }
    }

    public function abandonCallReport()
    {
        if($this->users) {
            $users = Cache::get('logged_in_users', []);
            $date1 = Carbon::now()->startOfDay()->format("Y-m-d");
            $date2 = Carbon::now()->endOfDay()->format("Y-m-d");

            foreach ($this->users as $user) {

                $pagination = $users[$user->id]['pagination']['abandonCalls'] ?? ['current' => 1, 'pageSize' => 15];
                $current = $pagination['current'];
                $pageSize = $pagination['pageSize'];

                $data = QueueLog::query()->from('queue_log as q')
                        // ->join('cdr as c', function ($join) {
                        //     $join->on('c.uniqueid', '=', 'q.callid')->where('q.EVENT', '=', 'ABANDON');
                        // })
                        ->join('cdr as c', 'c.uniqueid', '=', 'q.callid')
                        ->select(
                            DB::raw('DATE(q.time) as date'),
                            DB::raw('TIME(q.time) as time'),
                            'c.uniqueid',
                            'q.callid',
                            'c.src',
                            'c.dst',
                            'q.data3 as waittime',
                            'q.EVENT',
                            'q.status'
                        )
                        // ->whereDate('q.time', '>=', $date1)
                        // ->whereDate('q.time', '<=', $date2)
                        // ->where('q.status', '=', '0')
                        // ->groupBy('c.uniqueid');
                        ->where('q.EVENT', 'ABANDON')
                        ->whereBetween(DB::raw('DATE(q.time)'), [$date1, $date2])
                        ->where('q.status', '0');

                $paginatedData = $data->paginate($pageSize, ['*'], 'page', $current);

                $response = [
                    'user_id' => $user->id,
                    'current' => $paginatedData->currentPage(),
                    'pageSize' => $paginatedData->perPage(),
                    'total' => $paginatedData->total(),
                    'data' => ['data' => $paginatedData->items()],
                ];

                broadcast(new \App\Events\GetAbandonCallReport($response));
            }
        }
    }

    public function get_campaign()
    {
        if($this->users) {
            foreach ($this->users as $user) {
                // replace custom_numbers by campaign_numbers
                $data = $user->campaigns()->withCount(['campaign_numbers as count' => function($query) {
                                    $query->where('status', false)->where('attempts', '>', 0);
                                }])->get();
                $campaignData = ['user_id' => $user->id, 'data' => $data];

                broadcast(new GetUserCampagin($campaignData));
            }
        }
    }

    public function getSystemSetting()
    {
        if($this->users) {
            $data = SystemSetting::query()->get();

            foreach ($this->users as $user) {
                $setting=null;
                $myObj = new \stdClass();
                foreach ($data as $value)
                {
                    $id= $value->id;
                    $column[]=array('title'=> $value->key, 'dataIndex'=> $value->key, 'key'=> $value->key);
                    $key= $value->key;
                    $myObj->$key= $value->value;
                }
                $setting= array($myObj);
                $column[] = array('title'=> 'action', 'dataIndex'=> 'action', 'key'=>'action');

                $settingData = ['user_id' => $user->id, 'data' => $setting];

                broadcast(new GetSystemSetting($settingData));
            }
        }
    }

    public function get_user()
    {
        if($this->users) {
            foreach ($this->users as $user) {
                $userData = ['user_id' => $user->id, 'data' => $user];

                broadcast(new GetUser($userData));
            }
        }
    }

    public function callbackRequestReport()
    {
        if($this->users) {
            $users = Cache::get('logged_in_users', []);
            $date1 = Carbon::now()->startOfDay()->format("Y-m-d");
            $date2 = Carbon::now()->endOfDay()->format("Y-m-d");

            foreach ($this->users as $user) {

                $pagination = $users[$user->id]['pagination']['callbackRequests'] ?? ['current' => 1, 'pageSize' => 15];
                $current = $pagination['current'];
                $pageSize = $pagination['pageSize'];

                $data = CallbackRequest::query()->from('callback_requests as c')
                        ->select(
                            'c.id',
                            'c.caller_id',
                            'c.queue',
                            'c.request_time',
                            DB::raw('DATE(c.request_time) as date'),
                            DB::raw('TIME(c.request_time) as time'),
                            'c.filePath',
                            'c.fileName',
                            'c.fileLoc'
                        )
                        ->whereDate('c.request_time', '>=', $date1)
                        ->whereDate('c.request_time', '<=', $date2)
                        ->where('c.status', '=', '0');

                $paginatedData = $data->paginate($pageSize, ['*'], 'page', $current);

                $response = [
                    'user_id' => $user->id,
                    'currentPage' => $paginatedData->currentPage(),
                    'pageSize' => $paginatedData->perPage(),
                    'total' => $paginatedData->total(),
                    'data' => ['data' => $paginatedData->items()],
                ];

                broadcast(new \App\Events\GetCallbackRequest($response));

            }
        }
    }
}